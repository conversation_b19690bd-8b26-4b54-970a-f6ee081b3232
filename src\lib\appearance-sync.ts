/**
 * Real-time Appearance Settings Synchronization
 * Ensures consistent background settings across all application instances
 */

import type { AppearanceSettings } from './appearance-service'
import { logger } from './logger'
import { supabase } from './supabase'

export class AppearanceSync {
  private static listeners: Set<(settings: AppearanceSettings) => void> = new Set()
  private static subscription: any = null
  private static isInitialized = false
  private static retryCount = 0
  private static maxRetries = 3
  private static retryTimeout: NodeJS.Timeout | null = null
  private static pollingInterval: NodeJS.Timeout | null = null
  private static lastKnownSettings: AppearanceSettings | null = null
  private static isPollingMode = false

  /**
   * Initialize real-time synchronization
   */
  static initialize() {
    if (this.isInitialized) {
      logger.info('🔄 Appearance sync already initialized')
      return
    }

    // Skip real-time and use polling mode for stability
    logger.info('🔄 Initializing appearance settings sync (polling mode)')
    this.startPollingMode()
  }

  /**
   * Setup real-time subscription with retry logic
   */
  private static setupRealtimeSubscription() {
    try {
      // Clear any existing retry timeout
      if (this.retryTimeout) {
        clearTimeout(this.retryTimeout)
        this.retryTimeout = null
      }

      // Skip if already initialized and working
      if (this.isInitialized && this.subscription) {
        logger.info('Real-time sync already initialized, skipping setup')
        return
      }

      // Subscribe to real-time changes
      this.subscription = supabase
        .channel('appearance_settings_changes')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'appearance_settings',
            filter: 'setting_key=eq.landing_page_background'
          },
          (payload) => {
            logger.info('🔄 Real-time appearance settings change detected', {
              eventType: payload.eventType,
              table: payload.table,
              schema: payload.schema
            })
            this.handleSettingsChange(payload)
          }
        )
        .subscribe((status, err) => {
          logger.info('🔄 Subscription status change', {
            status,
            error: err instanceof Error ? err.message : String(err),
            retryCount: this.retryCount
          })

          if (status === 'SUBSCRIBED') {
            logger.info('✅ Real-time appearance sync initialized')
            this.isInitialized = true
            this.retryCount = 0 // Reset retry count on successful connection
          } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
            // Only log as warning for connection issues, not error
            logger.warn(`⚠️ Real-time sync ${status.toLowerCase()}`, {
              error: err instanceof Error ? err.message : String(err),
              retryCount: this.retryCount
            })
            this.handleConnectionError()
          } else if (status === 'CLOSED') {
            logger.info('ℹ️ Real-time sync channel closed', {
              error: err instanceof Error ? err.message : String(err),
              retryCount: this.retryCount
            })
            this.handleConnectionError()
          }
        })

      // Listen for custom events within the same tab only
      if (typeof window !== 'undefined') {
        window.addEventListener('appearance-settings-updated', this.handleCustomEvent.bind(this) as EventListener)
      }

    } catch (error) {
      logger.warn('Failed to setup realtime subscription', {
        error: error instanceof Error ? error.message : String(error),
        retryCount: this.retryCount
      })
      this.handleConnectionError()
    }
  }

  /**
   * Handle connection errors with retry logic
   */
  private static handleConnectionError() {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++
      const retryDelay = Math.min(1000 * Math.pow(2, this.retryCount - 1), 10000) // Exponential backoff, max 10s

      logger.info(`🔄 Retrying real-time connection in ${retryDelay}ms (attempt ${this.retryCount}/${this.maxRetries})`)

      this.retryTimeout = setTimeout(() => {
        this.cleanup()
        this.setupRealtimeSubscription()
      }, retryDelay)
    } else {
      logger.warn('⚠️ Max retry attempts reached for real-time sync. Switching to polling mode.')
      this.startPollingMode()
    }
  }

  /**
   * Start polling mode as fallback when real-time fails
   */
  private static startPollingMode() {
    if (this.isPollingMode) {
      return // Already in polling mode
    }

    this.isPollingMode = true
    this.isInitialized = true
    logger.info('🔄 Starting polling mode for appearance sync (every 5 seconds)')

    // Initial sync
    this.pollForChanges()

    // Set up polling interval
    this.pollingInterval = setInterval(() => {
      this.pollForChanges()
    }, 5000) // Poll every 5 seconds
  }

  /**
   * Poll for changes in polling mode
   */
  private static async pollForChanges() {
    try {
      const { data, error } = await supabase
        .from('appearance_settings')
        .select('setting_value, updated_at')
        .eq('setting_key', 'landing_page_background')
        .single()

      if (error) {
        logger.error('❌ Polling failed', { error: error.message })
        return
      }

      if (data?.setting_value) {
        const settings = data.setting_value as AppearanceSettings

        // Check if settings have changed
        if (!this.lastKnownSettings ||
            JSON.stringify(this.lastKnownSettings) !== JSON.stringify(settings)) {

          logger.info('🔄 Polling detected settings change', {
            backgroundType: settings.backgroundType,
            hasImage: !!settings.backgroundImage
          })

          this.lastKnownSettings = settings
          this.broadcastChange(settings)
        }
      }
    } catch (error) {
      logger.error('❌ Error during polling', { error })
    }
  }

  /**
   * Stop polling mode
   */
  private static stopPollingMode() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval)
      this.pollingInterval = null
    }
    this.isPollingMode = false
    logger.info('🛑 Polling mode stopped')
  }

  /**
   * Cleanup subscriptions
   */
  static cleanup() {
    // Clear retry timeout
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout)
      this.retryTimeout = null
    }

    // Stop polling mode
    this.stopPollingMode()

    // Remove subscription
    if (this.subscription) {
      supabase.removeChannel(this.subscription)
      this.subscription = null
    }

    // Remove event listeners
    if (typeof window !== 'undefined') {
      window.removeEventListener('appearance-settings-updated', this.handleCustomEvent.bind(this) as EventListener)
    }

    // Reset state
    this.listeners.clear()
    this.isInitialized = false
    this.retryCount = 0
    this.lastKnownSettings = null

    logger.info('🧹 Appearance sync cleanup completed')
  }

  /**
   * Add a listener for settings changes
   */
  static addListener(callback: (settings: AppearanceSettings) => void) {
    this.listeners.add(callback)
    logger.info(`📡 Added appearance settings listener (${this.listeners.size} total)`)

    // Return cleanup function
    return () => {
      this.listeners.delete(callback)
      logger.info(`📡 Removed appearance settings listener (${this.listeners.size} remaining)`)
    }
  }

  /**
   * Broadcast settings change to all listeners
   */
  static broadcastChange(settings: AppearanceSettings) {
    logger.info('📢 Broadcasting appearance settings change', { 
      backgroundType: settings.backgroundType,
      hasImage: !!settings.backgroundImage,
      listenerCount: this.listeners.size
    })

    // Notify all listeners
    this.listeners.forEach(callback => {
      try {
        callback(settings)
      } catch (error) {
        logger.error('Error in appearance settings listener', { error })
      }
    })

    // Broadcast to same-tab components only (no localStorage)
    if (typeof window !== 'undefined') {
      // Dispatch custom event for same-tab communication
      window.dispatchEvent(new CustomEvent('appearance-settings-updated', {
        detail: settings
      }))
    }
  }

  /**
   * Get connection status for debugging
   */
  static getConnectionStatus() {
    return {
      isInitialized: this.isInitialized,
      hasSubscription: !!this.subscription,
      retryCount: this.retryCount,
      maxRetries: this.maxRetries,
      hasRetryTimeout: !!this.retryTimeout,
      isPollingMode: this.isPollingMode,
      hasPollingInterval: !!this.pollingInterval,
      listenerCount: this.listeners.size,
      hasLastKnownSettings: !!this.lastKnownSettings
    }
  }

  /**
   * Force sync from database
   */
  static async forceSyncFromDatabase(): Promise<AppearanceSettings | null> {
    try {
      logger.info('🔄 Force syncing appearance settings from database')

      const { data, error } = await supabase
        .from('appearance_settings')
        .select('setting_value')
        .eq('setting_key', 'landing_page_background')
        .single()

      if (error) {
        logger.error('Failed to force sync from database', { error })
        return null
      }

      if (data?.setting_value) {
        const settings = data.setting_value as AppearanceSettings
        logger.info('✅ Force sync successful', { 
          backgroundType: settings.backgroundType,
          hasImage: !!settings.backgroundImage
        })
        
        this.broadcastChange(settings)
        return settings
      }

      return null
    } catch (error) {
      logger.error('Error during force sync', { error })
      return null
    }
  }

  /**
   * Handle real-time database changes
   */
  private static handleSettingsChange(payload: any) {
    try {
      const { new: newRecord, eventType } = payload

      if (eventType === 'DELETE') {
        logger.info('🗑️ Appearance settings deleted')
        return
      }

      if (newRecord?.setting_value) {
        const settings = newRecord.setting_value as AppearanceSettings
        logger.info('🔄 Database change detected', { 
          eventType,
          backgroundType: settings.backgroundType,
          hasImage: !!settings.backgroundImage
        })

        this.broadcastChange(settings)
      }
    } catch (error) {
      logger.error('Error handling settings change', { error })
    }
  }



  /**
   * Handle custom events (same-tab communication)
   */
  private static handleCustomEvent(event: CustomEvent) {
    if (event.detail) {
      logger.info('🔄 Same-tab settings change detected')
      // Don't re-broadcast to avoid loops
      this.listeners.forEach(callback => {
        try {
          callback(event.detail)
        } catch (error) {
          logger.error('Error in custom event listener', { error })
        }
      })
    }
  }
}

// Auto-initialize when module loads (client-side only)
if (typeof window !== 'undefined') {
  AppearanceSync.initialize()
  
  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    AppearanceSync.cleanup()
  })
}
